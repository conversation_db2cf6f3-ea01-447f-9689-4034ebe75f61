#!/bin/bash

# Test script to verify DigitalOcean API connectivity
# Usage: ./test_do_api.sh [API_TOKEN]

set -e

# Use provided token or environment variable
API_TOKEN=${1:-$DO_API_TOKEN}

if [ -z "$API_TOKEN" ]; then
    echo "❌ Error: No API token provided"
    echo "Usage: $0 [API_TOKEN]"
    echo "Or set DO_API_TOKEN environment variable"
    exit 1
fi

echo "🔍 Testing DigitalOcean API connectivity..."

# Test basic API access
echo "📡 Testing API authentication..."
response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $API_TOKEN" \
    "https://api.digitalocean.com/v2/account" -o /tmp/do_test_response.json)

if [ "$response" = "200" ]; then
    echo "✅ API authentication successful"
    account_email=$(cat /tmp/do_test_response.json | grep -o '"email":"[^"]*"' | cut -d'"' -f4)
    echo "   Account: $account_email"
else
    echo "❌ API authentication failed (HTTP $response)"
    cat /tmp/do_test_response.json
    exit 1
fi

# Test database listing
echo "📊 Testing database listing..."
response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $API_TOKEN" \
    "https://api.digitalocean.com/v2/databases" -o /tmp/do_databases_response.json)

if [ "$response" = "200" ]; then
    echo "✅ Database listing successful"
    
    # Parse and display database info
    if command -v jq >/dev/null 2>&1; then
        echo "   Found databases:"
        cat /tmp/do_databases_response.json | jq -r '.databases[] | "   - \(.name) (\(.engine), \(.status))"'
    else
        echo "   (Install jq for detailed database info)"
        db_count=$(grep -o '"name":' /tmp/do_databases_response.json | wc -l)
        echo "   Found $db_count database clusters"
    fi
else
    echo "❌ Database listing failed (HTTP $response)"
    cat /tmp/do_databases_response.json
    exit 1
fi

# Test the Ruby script
echo "🔧 Testing Ruby script..."
if [ -f "./fetch_do_databases.rb" ]; then
    if command -v ruby >/dev/null 2>&1; then
        echo "   Running database discovery script..."
        ./fetch_do_databases.rb --token "$API_TOKEN" --command production | head -10
        echo "✅ Ruby script executed successfully"
    else
        echo "⚠️  Ruby not found - script will run in CI environment"
    fi
else
    echo "❌ fetch_do_databases.rb not found"
    exit 1
fi

# Cleanup
rm -f /tmp/do_test_response.json /tmp/do_databases_response.json

echo ""
echo "🎉 All tests passed! The DigitalOcean API integration is ready."
echo ""
echo "Next steps:"
echo "1. Add DO_API_TOKEN to your GitLab CI/CD variables"
echo "2. Test the pipeline with a manual run"
echo "3. Verify the scheduled backup works as expected"

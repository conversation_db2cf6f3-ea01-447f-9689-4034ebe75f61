# Dynamic Database Backup with DigitalOcean API

This setup replaces the static list of databases in the GitLab CI pipeline with a dynamic approach that fetches the current list of databases from the DigitalOcean API.

## Overview

The backup process now consists of two main jobs:

1. **`fetch_database_list`** - Fetches the list of production databases from DigitalOcean API
2. **`monthly_backup_dynamic`** - Backs up all databases found in the previous step
3. **`********************`** - Transfers the backup files to the backup server

## Required Environment Variables

Make sure the following environment variables are set in your GitLab CI/CD settings:

### Existing Variables
- `POSTGRES_PASSWORD_PRODUCTION` - Password for the production PostgreSQL cluster
- Other existing database connection variables

### New Variables Required
- `DO_API_TOKEN` - DigitalOcean API token with read access to databases

## How It Works

### 1. Database Discovery
The `fetch_database_list` job uses the `fetch_do_databases.rb` script to:
- Connect to the DigitalOcean API
- Find all PostgreSQL clusters that are online and not test/clone databases
- Extract individual database names from each cluster
- Generate a list of databases to backup

### 2. Dynamic Backup
The `monthly_backup_dynamic` job:
- Reads the database list generated in step 1
- Iterates through each database
- Creates a pg_dump backup for each database
- Handles errors gracefully (continues if one database fails)

### 3. Transfer
The `********************` job:
- Moves all backup files to a dated directory
- Cleans up old backups (older than 72 days)
- Provides better error handling and logging

## Files Added/Modified

### New Files
- `fetch_do_databases.rb` - Script to fetch database list from DigitalOcean API
- `DYNAMIC_BACKUP_README.md` - This documentation

### Modified Files
- `.gitlab-ci.yml` - Updated backup jobs to use dynamic database discovery

## Testing

To test the database discovery locally:

```bash
# Test with your API token
./fetch_do_databases.rb --token YOUR_DO_API_TOKEN --command production

# See all databases with details
./fetch_do_databases.rb --token YOUR_DO_API_TOKEN --command all
```

## Rollback Plan

If you need to rollback to the static list approach, you can:

1. Uncomment the old `monthly_backup` job in `.gitlab-ci.yml`
2. Update the `********************` job to depend on `monthly_backup` instead of `monthly_backup_dynamic`
3. Comment out or remove the new dynamic jobs

## Customization

### Filtering Databases
You can modify the database filtering logic in `fetch_do_databases.rb` in the `get_production_databases` method. Current filters:
- Engine must be 'pg' (PostgreSQL)
- Status must be 'online'
- Name must not contain 'test' or 'clone'

### Backup Parameters
You can modify backup parameters in the `monthly_backup_dynamic` job script section of `.gitlab-ci.yml`.

## Monitoring

The new setup provides better logging:
- Shows which databases are being backed up
- Indicates success/failure for each database
- Lists files in the backup directory
- Provides error messages for troubleshooting

## Security Notes

- The DigitalOcean API token should have minimal required permissions (read-only access to databases)
- Store the API token as a protected variable in GitLab CI/CD settings
- Consider using a dedicated service account for API access

image: docker:latest
services:
  - docker:dind

stages:
  - backup
  - restore
  - transfer

# greenmask cloning backup with masking

# .backup_template:
#   stage: backup
#   image:
#     name: greenmask/greenmask:v0.1.14
#     entrypoint:
#       - ''
#   script:
#     - mkdir -p dump/${NUB}
#     - PGUSER=root PGPASSWORD=$POSTGRES_PASSWORD_HO greenmask dump
#       --config=config.yml --clean --dbname=${NUB} --port=5432
#       --host=************** --file dump/${NUB}
#     - greenmask --config=config.yml list-dumps
#     - greenmask --config=config.yml show-dump latest
#     - ls -la
#     - ls dump/${NUB}
#     # - PGUSER=doadmin PGPASSWORD=$POSTGRES_PASSWORD greenmask restore latest --config=config.yml --clean --dbname=${NUB}-clone --port=25060 --host=db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com --no-owner=true
#     # - PGPASSWORD=$POSTGRES_PASSWORD psql -h db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com -p 25060 -d ${NUB}-clone -U doadmin -c "ALTER DATABASE \"${NUB}-clone\" OWNER TO $(echo ${NUB} | sed "s/-/_/g")_clone"

#   artifacts:
#     paths:
#       - dump/*
#     expire_in: 1 week
#   tags:
#     - docker-tests
#   when: manual
#   # rules:
#   #   - if: $CI_PIPELINE_SOURCE == "schedule"

backup_all_databases:
  <<:
  
    variables:
      CONFIG_FILE: "config.yml"
    stage: backup
    image:
      name: greenmask/greenmask:v0.2.7
      entrypoint:
        - ''
    script:
      - mkdir -p dumps
      - echo "Using config file ${CONFIG_FILE}"
      - PGUSER=root PGPASSWORD=$POSTGRES_PASSWORD_HO greenmask dump
        --config=${CONFIG_FILE} --clean --dbname=${NUB} --port=5432
        --host=************** --file dump/${NUB}
      - greenmask --config=${CONFIG_FILE} list-dumps
      - greenmask --config=${CONFIG_FILE} show-dump latest
      - ls -la
      - ls dumps
      - pwd
      - echo "${NUB_CLONE}"
    # - PGUSER=doadmin PGPASSWORD=$POSTGRES_PASSWORD greenmask restore latest --config=config.yml --clean --dbname=${NUB}-clone --port=25060 --host=db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com --no-owner=true
    # - PGPASSWORD=$POSTGRES_PASSWORD psql -h db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com -p 25060 -d ${NUB}-clone -U doadmin -c "ALTER DATABASE \"${NUB}-clone\" OWNER TO $(echo ${NUB} | sed "s/-/_/g")_clone"

    artifacts:
      paths:
        - "dumps/*"
      expire_in: 1 week
    tags:
      - docker-tests
    when: manual
  parallel:
    matrix:
      - NUB: api-prod
        NUB_CLONE: api-clone
      - NUB: autobot
        NUB_CLONE: autobot-clone
        CONFIG_FILE: config_backup.yml
      - NUB: behandle
        NUB_CLONE: behandle-clone
      - NUB: billing
        NUB_CLONE: billing-clone
      - NUB: business_rules
        NUB_CLONE: business-rules-clone
      - NUB: cms
        NUB_CLONE: cms-clone
      - NUB: filestore
        NUB_CLONE: filestore-clone
      - NUB: forms-master
        NUB_CLONE: forms-clone
      - NUB: mapping-master
        NUB_CLONE: mapping-clone
      - NUB: numbers
        NUB_CLONE: numbers-clone
      - NUB: paglipat
        NUB_CLONE: paglipat-clone
      - NUB: platform-admin
        NUB_CLONE: platform-admin-clone
      - NUB: policy-data
        NUB_CLONE: policy-data-clone
      - NUB: postgres
        NUB_CLONE: postgres-clone
      - NUB: service-tracker-master
        NUB_CLONE: service-tracker-clone
      - NUB: users-master
        NUB_CLONE: users-clone
      - NUB: vinna
        NUB_CLONE: vinna-clone
      - NUB: workflow
        NUB_CLONE: workflow-clone
        CONFIG_FILE: config_backup.yml
      - NUB: pharos
        NUB_CLONE: pharos-clone




backup_all_databases_no_masking:
  <<:

    stage: backup
    image:
      name: postgres:alpine
      entrypoint:
        - ''
    script:
      - PGUSER=root PGPASSWORD=$POSTGRES_PASSWORD_HO pg_dump -h ************** -p 5432 -Fc ${NUB} > ${NUB}.pgsql
    artifacts:
      paths:
        - "*.pgsql"
      expire_in: 1 week
    tags:
      - docker-tests
    when: manual
  parallel:
    matrix:
      - NUB: api-prod
        NUB_CLONE: api-clone
      - NUB: autobot
        NUB_CLONE: autobot-clone
        CONFIG_FILE: config_backup.yml
      - NUB: behandle
        NUB_CLONE: behandle-clone
      - NUB: billing
        NUB_CLONE: billing-clone
      - NUB: business_rules
        NUB_CLONE: business-rules-clone
      - NUB: cms
        NUB_CLONE: cms-clone
      - NUB: filestore
        NUB_CLONE: filestore-clone
      - NUB: forms-master
        NUB_CLONE: forms-clone
      - NUB: mapping-master
        NUB_CLONE: mapping-clone
      - NUB: numbers
        NUB_CLONE: numbers-clone
      - NUB: paglipat
        NUB_CLONE: paglipat-clone
      - NUB: platform-admin
        NUB_CLONE: platform-admin-clone
      - NUB: policy-data
        NUB_CLONE: policy-data-clone
      - NUB: postgres
        NUB_CLONE: postgres-clone
      - NUB: service-tracker-master
        NUB_CLONE: service-tracker-clone
      - NUB: users-master
        NUB_CLONE: users-clone
      - NUB: vinna
        NUB_CLONE: vinna-clone
      - NUB: workflow
        NUB_CLONE: workflow-clone
        CONFIG_FILE: config_backup.yml
      - NUB: pharos
        NUB_CLONE: pharos-clone


restore_all_databases_no_masking:
  <<:
    stage: restore
    image:
      name: postgres:alpine
      entrypoint:
        - ''
    variables:
      PGPASSWORD: $POSTGRES_PASSWORD
    script:
      - |
        psql -h db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com \
            -p 25060 \
            -d defaultdb \
            -U doadmin <<EOF
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = '${NUB_CLONE}' AND pid <> pg_backend_pid();

        DROP DATABASE IF EXISTS "${NUB_CLONE}";
        EOF

      - PGPASSWORD=$POSTGRES_PASSWORD psql -h db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com -p 25060 -d defaultdb -U doadmin -c "CREATE DATABASE \"${NUB_CLONE}\" OWNER $(echo ${NUB_CLONE} | sed "s/-/_/g");"

      - PGUSER=doadmin PGPASSWORD=$POSTGRES_PASSWORD  pg_restore -h "db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com" -p "25060" --no-owner -d ${NUB_CLONE} -c -v ${NUB}.pgsql

      - PGPASSWORD=$POSTGRES_PASSWORD psql -h db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com -p 25060 -d ${NUB_CLONE} -U doadmin -c "REASSIGN OWNED BY  \"doadmin\" TO $(echo ${NUB_CLONE} | sed "s/-/_/g")"

    tags:
      - dmitri
    when: manual
  dependencies:
    - backup_all_databases_no_masking # Ensures artifacts are available
  parallel:
    matrix:
      - NUB: api-prod
        NUB_CLONE: api-clone
      - NUB: autobot
        NUB_CLONE: autobot-clone
        CONFIG_FILE: config_backup.yml
      - NUB: behandle
        NUB_CLONE: behandle-clone
      - NUB: billing
        NUB_CLONE: billing-clone
      - NUB: business_rules
        NUB_CLONE: business-rules-clone
      - NUB: cms
        NUB_CLONE: cms-clone
      - NUB: filestore
        NUB_CLONE: filestore-clone
      - NUB: forms-master
        NUB_CLONE: forms-clone
      - NUB: mapping-master
        NUB_CLONE: mapping-clone
      - NUB: numbers
        NUB_CLONE: numbers-clone
      - NUB: paglipat
        NUB_CLONE: paglipat-clone
      - NUB: platform-admin
        NUB_CLONE: platform-admin-clone
      - NUB: policy-data
        NUB_CLONE: policy-data-clone
      - NUB: postgres
        NUB_CLONE: postgres-clone
      - NUB: service-tracker-master
        NUB_CLONE: service-tracker-clone
      - NUB: users-master
        NUB_CLONE: users-clone
      - NUB: vinna
        NUB_CLONE: vinna-clone
      - NUB: workflow
        NUB_CLONE: workflow-clone
        CONFIG_FILE: config_backup.yml
      - NUB: pharos
        NUB_CLONE: pharos-clone




restore_all_databases:
  <<:
    stage: restore
    image:
      name: greenmask/greenmask:v0.2.7
      entrypoint:
        - ''
    variables:
      CONFIG_FILE: "config.yml"
    script:
      - greenmask --config=${CONFIG_FILE} list-dumps
      - greenmask --config=${CONFIG_FILE} show-dump latest
      - ls -la
      - ls dumps
      - pwd
      - echo "${NUB_CLONE}"
      - PGPASSWORD=$POSTGRES_PASSWORD psql -h db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com -p 25060 -d defaultdb -U doadmin -c "DROP DATABASE IF EXISTS \"${NUB_CLONE}\";"
      - PGPASSWORD=$POSTGRES_PASSWORD psql -h db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com -p 25060 -d defaultdb -U doadmin -c "CREATE DATABASE \"${NUB_CLONE}\" OWNER $(echo ${NUB_CLONE} | sed "s/-/_/g");"
      - PGUSER=doadmin PGPASSWORD=$POSTGRES_PASSWORD greenmask restore latest --config=${CONFIG_FILE} --clean --dbname=${NUB_CLONE} --port=25060 --host=db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com --no-owner=true
      # - PGPASSWORD=$POSTGRES_PASSWORD psql -h db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com -p 25060 -d ${NUB_CLONE} -U doadmin -c "ALTER DATABASE \"${NUB_CLONE}\" OWNER TO $(echo ${NUB_CLONE} | sed "s/-/_/g")"
      - PGPASSWORD=$POSTGRES_PASSWORD psql -h db-postgresql-test-do-user-2355736-0.c.db.ondigitalocean.com -p 25060 -d ${NUB_CLONE} -U doadmin -c "REASSIGN OWNED BY  \"doadmin\" TO $(echo ${NUB_CLONE} | sed "s/-/_/g")"


    tags:
      - dmitri
    when: manual
  dependencies:
    - backup_all_databases # Ensures artifacts are available
  parallel:
    matrix:
      - NUB: api-prod
        NUB_CLONE: api-clone
      - NUB: autobot
        NUB_CLONE: autobot-clone
        CONFIG_FILE: config_backup.yml
      - NUB: behandle
        NUB_CLONE: behandle-clone
      - NUB: billing
        NUB_CLONE: billing-clone
      - NUB: business_rules
        NUB_CLONE: business-rules-clone
      - NUB: cms
        NUB_CLONE: cms-clone
      - NUB: filestore
        NUB_CLONE: filestore-clone
      - NUB: forms-master
        NUB_CLONE: forms-clone
      - NUB: mapping-master
        NUB_CLONE: mapping-clone
      - NUB: numbers
        NUB_CLONE: numbers-clone
      - NUB: paglipat
        NUB_CLONE: paglipat-clone
      - NUB: platform-admin
        NUB_CLONE: platform-admin-clone
      - NUB: policy-data
        NUB_CLONE: policy-data-clone
      - NUB: postgres
        NUB_CLONE: postgres-clone
      - NUB: service-tracker-master
        NUB_CLONE: service-tracker-clone
      - NUB: users-master
        NUB_CLONE: users-clone
      - NUB: vinna
        NUB_CLONE: vinna-clone
      - NUB: workflow
        NUB_CLONE: workflow-clone
        CONFIG_FILE: config_backup.yml
      - NUB: pharos
        NUB_CLONE: pharos-clone




# DEPRECATED: Old static monthly backup job - replaced by monthly_backup_dynamic
# monthly_backup:
#   stage: backup
#   image:
#     name: postgres:alpine
#   parallel:
#     matrix:
#       - NUB:
#           - agentdash
#           - appetiteguide
#           - auto_adjuster
#           - board-website
#           - core
#           - dashboard-admin-production
#           - healthchecks
#           - my-policy-plus
#           - projectrequestsystem
#           - remindersremarks
#           - trove
#           - web-admins
#           - website
#   script:
#     - PGPASSWORD=$POSTGRES_PASSWORD_PRODUCTION pg_dump -h
#       db-postgresql-production-do-user-2355736-0.c.db.ondigitalocean.com -U
#       doadmin -p 25060 -Fc ${NUB} > ${NUB}.pgsql
#   artifacts:
#     paths:
#       - '*.pgsql'
#     expire_in: 1 week
#   rules:
#     - if: $CI_PIPELINE_SOURCE == "schedule"
#   tags:
#     - dmitri

# Job to fetch database list from DigitalOcean API
fetch_database_list:
  stage: backup
  image:
    name: ruby:3.1-alpine
  before_script:
    - apk add --no-cache curl
    - chmod +x ./fetch_do_databases.rb
  script:
    - ./fetch_do_databases.rb --token $DO_API_TOKEN --command production > database_list.txt
    - echo "Found databases:"
    - cat database_list.txt
    - |
      # Convert database list to GitLab CI matrix format
      echo "databases:" > databases.yml
      while IFS= read -r db_name; do
        # Skip comments and empty lines
        if [[ ! "$db_name" =~ ^#.*$ ]] && [[ -n "$db_name" ]]; then
          echo "  - \"$db_name\"" >> databases.yml
        fi
      done < database_list.txt
    - echo "Generated matrix:"
    - cat databases.yml
  artifacts:
    paths:
      - database_list.txt
      - databases.yml
    expire_in: 1 hour
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
    - when: manual
  tags:
    - dmitri

# Dynamic monthly backup using DigitalOcean API
monthly_backup_dynamic:
  stage: backup
  image:
    name: postgres:alpine
  needs:
    - fetch_database_list
  script:
    - |
      # Read database list and backup each one
      while IFS= read -r db_name; do
        # Skip comments and empty lines
        if [[ ! "$db_name" =~ ^#.*$ ]] && [[ -n "$db_name" ]]; then
          echo "Backing up database: $db_name"
          PGPASSWORD=$POSTGRES_PASSWORD_PRODUCTION pg_dump \
            -h db-postgresql-production-do-user-2355736-0.c.db.ondigitalocean.com \
            -U doadmin \
            -p 25060 \
            -Fc "$db_name" > "${db_name}.pgsql"

          if [ $? -eq 0 ]; then
            echo "✅ Successfully backed up $db_name"
          else
            echo "❌ Failed to backup $db_name"
          fi
        fi
      done < database_list.txt
  artifacts:
    paths:
      - '*.pgsql'
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
    - when: manual
  tags:
    - dmitri

sftp_backup_transfer:
  stage: transfer
  needs:
    - monthly_backup_dynamic
  script:
    - BACKUP_DIR="/opt/backups/$(date +%Y-%m-%d)"
    - mkdir -p "$BACKUP_DIR"
    - |
      # Check if any backup files exist
      if ls *.pgsql 1> /dev/null 2>&1; then
        mv *.pgsql "$BACKUP_DIR/"
        echo "Backups moved to $BACKUP_DIR"
        echo "Files in backup directory:"
        ls -la "$BACKUP_DIR/"
      else
        echo "No backup files found to transfer"
        exit 1
      fi
    - echo "Cleaning up old backups (older than 72 days)"
    - find /opt/backups -maxdepth 1 -type d -mtime +72 -exec rm -rf {} \;
  dependencies:
    - monthly_backup_dynamic # Ensures artifacts are available
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
    - when: manual
  tags:
    - dbbackup



messup_database:
  image:
    name: postgres:alpine
  stage: restore
  variables:
    SOURCE_HOST: ""
    DEST_HOST: ""
    DATABASE_NAME: ""
  script:
    - chmod +x ./messup.sh
    - |
      if [ -n "$SOURCE_HOST" ] && [ -n "$DEST_HOST" ] && [ -n "$DATABASE_NAME" ]; then
        ./messup.sh -s "$SOURCE_HOST" -d "$DEST_HOST" -n "$DATABASE_NAME"
      elif [ -n "$DATABASE_NAME" ]; then
        ./messup.sh -n "$DATABASE_NAME"
      else
        echo "No parameters provided, using defaults from db_list.txt"
        ./messup.sh
      fi
  rules:
    - when: manual
  tags:
    - dmitri